# PowerShell script to fix ALL relative navigation links to absolute paths
# This will ensure 100% bug-free navigation

Write-Host "Starting comprehensive navigation link fix..." -ForegroundColor Green
Write-Host "This will fix ALL relative links to prevent duplicate URLs" -ForegroundColor Yellow

# Define the replacements for navigation links
$replacements = @{
    'href="../tax/'           = 'href="/tax/'
    'href="../loan/'          = 'href="/loan/'
    'href="../investment/'    = 'href="/investment/'
    'href="../health/'        = 'href="/health/'
    'href="../discount/'      = 'href="/discount/'
    'href="../blog/'          = 'href="/blog/'
    'href="../"'              = 'href="/"'
}

# Define the replacements for asset links (CSS, JS, images)
$assetReplacements = @{
    'href="../assets/'        = 'href="/assets/'
    'src="../assets/'         = 'src="/assets/'
    'href="../favicon.ico"'   = 'href="/favicon.ico"'
    'href="../favicon.svg"'   = 'href="/favicon.svg"'
}

# Get all HTML files
$htmlFiles = Get-ChildItem -Path "." -Filter "*.html" -Recurse

$totalFiles = $htmlFiles.Count
$processedFiles = 0

Write-Host "Found $totalFiles HTML files to process..." -ForegroundColor Cyan

foreach ($file in $htmlFiles) {
    $processedFiles++
    $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
    Write-Progress -Activity "Fixing navigation links" -Status "Processing $relativePath" -PercentComplete (($processedFiles / $totalFiles) * 100)
    
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    if (-not $content) { continue }
    
    $originalContent = $content
    $changesMade = $false
    
    # Fix navigation links
    foreach ($find in $replacements.Keys) {
        $replace = $replacements[$find]
        if ($content -match [regex]::Escape($find)) {
            $content = $content -replace [regex]::Escape($find), $replace
            $changesMade = $true
        }
    }
    
    # Fix asset links
    foreach ($find in $assetReplacements.Keys) {
        $replace = $assetReplacements[$find]
        if ($content -match [regex]::Escape($find)) {
            $content = $content -replace [regex]::Escape($find), $replace
            $changesMade = $true
        }
    }
    
    # Save the file if changes were made
    if ($changesMade) {
        try {
            Set-Content -Path $file.FullName -Value $content -NoNewline -ErrorAction Stop
            Write-Host "✓ Fixed: $relativePath" -ForegroundColor Green
        } catch {
            Write-Host "✗ Error fixing: $relativePath - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Progress -Activity "Fixing navigation links" -Completed

Write-Host "`nNavigation link fix completed!" -ForegroundColor Green
Write-Host "All relative links have been converted to absolute paths." -ForegroundColor Cyan
Write-Host "This eliminates duplicate URL issues like /tax/tax/free-income-tax/" -ForegroundColor Yellow

# Verification
Write-Host "`nRunning verification..." -ForegroundColor Cyan
$remainingRelativeLinks = Select-String -Path "*.html" -Pattern 'href="\.\./(?!assets)' -Recurse 2>$null
if ($remainingRelativeLinks) {
    Write-Host "⚠️  Found remaining relative navigation links:" -ForegroundColor Yellow
    $remainingRelativeLinks | ForEach-Object { Write-Host "  $($_.Filename):$($_.LineNumber) - $($_.Line.Trim())" }
} else {
    Write-Host "✅ No remaining relative navigation links found!" -ForegroundColor Green
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
